// Patient Details Inline Editing
// Handles inline editing of patient details with AJAX updates

document.addEventListener('DOMContentLoaded', () => {
  initInlineEditing();
});

/**
 * Initialize inline editing for patient details
 */
function initInlineEditing() {
  const form = document.getElementById('patient-details-form');
  if (!form) return;
  
  const patientId = form.dataset.patientId;
  if (!patientId) return;
  
  // Handle direct dropdown fields (like Title)
  document.querySelectorAll('.dropdown-field select').forEach(select => {
    // Save on change
    select.addEventListener('change', function() {
      saveDropdownField(this);
    });
  });
  
  // Handle date picker fields
  document.querySelectorAll('.date-picker-field input').forEach(datePicker => {
    // Save on change
    datePicker.addEventListener('change', function() {
      saveDatePickerField(this);
    });
  });
  
  // Handle calendar icon clicks
  document.querySelectorAll('.calendar-icon').forEach(icon => {
    icon.addEventListener('click', function() {
      // Find the associated date input
      const dateInput = this.closest('.date-picker-field').querySelector('input[type="date"]');
      if (dateInput) {
        // Focus and open the date picker
        dateInput.focus();
        dateInput.showPicker();
      }
    });
  });
  
  // Make regular fields editable on click
  document.querySelectorAll('.editable-field').forEach(field => {
    // Show edit field when clicking on display field
    field.addEventListener('click', function(e) {
      // Don't do anything if we're already editing
      if (this.classList.contains('editing')) return;
      
      this.classList.add('editing');
      
      // Hide display, show edit field
      const display = this.querySelector('.field-display');
      const edit = this.querySelector('.field-edit');
      
      if (display && edit) {
        display.classList.add('hidden');
        edit.classList.remove('hidden');
        
        // Focus the input/select
        const input = edit.querySelector('input, select');
        if (input) {
          // Initialize intl-tel-input for phone fields
          if (input.type === 'tel' && input.classList.contains('phonefield')) {
            // Check if intl-tel-input is already initialized
            if (!input.classList.contains('iti__tel-input')) {
              window.intlTelInput(input, {
                loadUtils: () => import("https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"),
                initialCountry: "au", // Set Australia as default
                preferredCountries: ["au", "nz", "us", "gb"],
                separateDialCode: true
              });
            }
          }

          input.focus();

          // For text inputs, position cursor at the end
          if (input.type === 'text') {
            input.selectionStart = input.selectionEnd = input.value.length;
          }
        }
      }
    });
  });
  
  // Handle saving on blur and enter key
  document.querySelectorAll('.editable-field input, .editable-field select').forEach(input => {
    // Save on blur
    input.addEventListener('blur', function() {
      saveField(this);
    });
    
    // Save on enter key
    input.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        this.blur(); // Trigger blur event which will save
      }
    });
  });
}

/**
 * Save a date picker field value via AJAX
 * @param {HTMLElement} datePicker - The date input element
 */
function saveDatePickerField(datePicker) {
  const field = datePicker.closest('.date-picker-field');
  if (!field) return;
  
  const fieldName = field.dataset.field;
  const form = document.getElementById('patient-details-form');
  const patientId = form.dataset.patientId;
  const value = datePicker.value;
  const originalValue = datePicker.getAttribute('data-original-value') || '';
  
  // Store original value for future reference
  if (!datePicker.hasAttribute('data-original-value')) {
    datePicker.setAttribute('data-original-value', value);
  }
  
  // Don't save if nothing changed
  if (value === originalValue) {
    return;
  }
  
  // Show loading state
  field.classList.remove('bg-white');
  field.classList.add('bg-blue-50');
  field.classList.add('border-blue-200');
  datePicker.classList.add('bg-blue-50');
  
  // Prepare data for AJAX request
  const data = {};
  data['patient[' + fieldName + ']'] = value;
  
  // Debug log
  console.log('Sending date picker data:', { fieldName, value, data });
  
  // Send AJAX request using jQuery
  $.ajax({
    url: '/admin/patients/' + patientId + '/update_field',
    method: 'POST',
    data: data,
    dataType: 'json',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        // Show success state
        field.classList.remove('bg-blue-50');
        field.classList.remove('border-blue-200');
        field.classList.add('bg-green-50');
        field.classList.add('border-green-200');
        
        // Also style the date picker
        datePicker.classList.remove('bg-blue-50');
        datePicker.classList.add('bg-green-50');
        
        // Update original value
        datePicker.setAttribute('data-original-value', value);
        
        // Update the header DOB if this is the date of birth field
        if (fieldName === 'date_of_birth') {
          updatePatientDOB(value);
        }

        // Show success message
        toastr.success('Date of birth updated successfully');
        
        // Reset field after a delay
        setTimeout(() => {
          field.classList.remove('bg-green-50');
          field.classList.remove('border-green-200');
          field.classList.add('bg-white');
          datePicker.classList.remove('bg-green-50');
        }, 1000);
      } else {
        // Revert to original value
        datePicker.value = originalValue;
        handleError(field, response.errors ? response.errors.join(', ') : 'Failed to update date of birth');
      }
    },
    error: function(xhr, status, error) {
      console.error('Error:', error);
      // Revert to original value
      datePicker.value = originalValue;
      handleError(field, 'Network error occurred: ' + error);
    }
  });
}

/**
 * Save a dropdown field value via AJAX
 * @param {HTMLElement} select - The select element
 */
function saveDropdownField(select) {
  const field = select.closest('.dropdown-field');
  if (!field) return;
  
  const fieldName = field.dataset.field;
  const form = document.getElementById('patient-details-form');
  const patientId = form.dataset.patientId;
  const value = select.value;
  const originalValue = select.getAttribute('data-original-value') || '';
  
  // Store original value for future reference
  if (!select.hasAttribute('data-original-value')) {
    select.setAttribute('data-original-value', value);
  }
  
  // Don't save if nothing changed
  if (value === originalValue) {
    return;
  }
  
  // Show loading state
  field.classList.remove('bg-white');
  field.classList.add('bg-blue-50');
  field.classList.add('border-blue-200');
  select.classList.add('bg-blue-50');
  
  // Prepare data for AJAX request
  const data = {};
  data['patient[' + fieldName + ']'] = value;
  
  // Debug log
  console.log('Sending dropdown data:', { fieldName, value, data });
  
  // Send AJAX request using jQuery
  $.ajax({
    url: '/admin/patients/' + patientId + '/update_field',
    method: 'POST',
    data: data,
    dataType: 'json',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        // Show success state
        field.classList.remove('bg-blue-50');
        field.classList.remove('border-blue-200');
        field.classList.add('bg-green-50');
        field.classList.add('border-green-200');
        
        // Also style the select
        select.classList.remove('bg-blue-50');
        select.classList.add('bg-green-50');
        
        // Update original value
        select.setAttribute('data-original-value', value);
        
        // Show success message
        toastr.success('Patient details updated successfully');
        
        // Update header if this is a field that affects the patient name
        if (fieldName === 'title') {
          updatePatientHeader();
        }

        // Reset field after a delay
        setTimeout(() => {
          field.classList.remove('bg-green-50');
          field.classList.remove('border-green-200');
          field.classList.add('bg-white');
          select.classList.remove('bg-green-50');
        }, 1000);
      } else {
        // Revert to original value
        select.value = originalValue;
        handleError(field, response.errors ? response.errors.join(', ') : 'Failed to update patient details');
      }
    },
    error: function(xhr, status, error) {
      console.error('Error:', error);
      // Revert to original value
      select.value = originalValue;
      handleError(field, 'Network error occurred: ' + error);
    }
  });
}

/**
 * Save a field value via AJAX
 * @param {HTMLElement} input - The input element
 */
function saveField(input) {
  const field = input.closest('.editable-field');
  if (!field) return;
  
  const fieldName = field.dataset.field;
  const form = document.getElementById('patient-details-form');
  const patientId = form.dataset.patientId;
  const value = input.value;
  
  // Store original value to revert if needed
  const display = field.querySelector('.field-display');
  const originalDisplayValue = display.textContent.trim();
  const originalInputValue = input.defaultValue;
  
  // Don't save if nothing changed
  if ((value === '' && originalDisplayValue === '-') || 
      (value && originalDisplayValue === value)) {
    resetField(field);
    return;
  }
  
  // Show loading state
  field.classList.remove('bg-white');
  field.classList.add('bg-blue-50');
  field.classList.add('border-blue-200');
  
  // Also style the input/select
  const inputElement = field.querySelector('input, select');
  if (inputElement) {
    inputElement.classList.add('bg-blue-50');
  }
  
  // Prepare data for AJAX request
  const data = {};
  data['patient[' + fieldName + ']'] = value;
  
  // Debug log
  console.log('Sending data:', { fieldName, value, data });
  
  // Send AJAX request using jQuery
  $.ajax({
    url: '/admin/patients/' + patientId + '/update_field',
    method: 'POST',
    data: data,
    dataType: 'json',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        // Show success state
        field.classList.remove('bg-blue-50');
        field.classList.remove('border-blue-200');
        field.classList.add('bg-green-50');
        field.classList.add('border-green-200');
        
        // Also style the input/select
        const inputElement = field.querySelector('input, select');
        if (inputElement) {
          inputElement.classList.remove('bg-blue-50');
          inputElement.classList.add('bg-green-50');
        }
        
        // Update display value
        updateDisplayValue(field, value);
        
        // Update header if this is a field that affects the patient name
        if (fieldName === 'first_name' || fieldName === 'last_name') {
          updatePatientHeader();
        }

        // Show success message
        toastr.success('Patient details updated successfully');
        
        // Reset field after a delay
        setTimeout(() => {
          field.classList.remove('bg-green-50');
          field.classList.remove('border-green-200');
          field.classList.add('bg-white');
          
          // Reset input/select styling
          const inputElement = field.querySelector('input, select');
          if (inputElement) {
            inputElement.classList.remove('bg-green-50');
          }
          
          resetField(field);
        }, 1000);
      } else {
        // Revert to original value
        input.value = originalInputValue;
        handleError(field, response.errors ? response.errors.join(', ') : 'Failed to update patient details');
      }
    },
    error: function(xhr, status, error) {
      console.error('Error:', error);
      // Revert to original value
      input.value = originalInputValue;
      handleError(field, 'Network error occurred: ' + error);
    }
  });
}

/**
 * Update the display value of a field
 * @param {HTMLElement} field - The field element
 * @param {string} value - The new value
 */
function updateDisplayValue(field, value) {
  const display = field.querySelector('.field-display');
  if (!display) return;
  
  const fieldName = field.dataset.field;
  
  // Handle special formatting for date fields
  if (fieldName === 'date_of_birth' && value) {
    const date = new Date(value);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    display.textContent = `${day}/${month}/${year}`;
  } else {
    // For other fields, just set the value or '-' if empty
    display.textContent = value || '-';
  }
  
  // Update styling based on whether there's a value
  if (value) {
    display.classList.remove('text-gray-400');
    display.classList.add('font-medium', 'text-gray-800');
  } else {
    display.classList.remove('font-medium', 'text-gray-800');
    display.classList.add('text-gray-400');
  }
}

/**
 * Handle error when saving a field
 * @param {HTMLElement} field - The field element
 * @param {string} errorMessage - The error message to display
 */
function handleError(field, errorMessage = 'Failed to update patient details') {
  // Show error state
  field.classList.remove('bg-blue-50');
  field.classList.remove('bg-white');
  field.classList.remove('border-blue-200');
  field.classList.add('bg-red-50');
  field.classList.add('border-red-200');
  
  // Also style the input/select
  const inputElement = field.querySelector('input, select');
  if (inputElement) {
    inputElement.classList.remove('bg-blue-50');
    inputElement.classList.add('bg-red-50');
  }
  
  // Show error message
  toastr.error(errorMessage);
  
  // Reset field after a delay
  setTimeout(() => {
    field.classList.remove('bg-red-50');
    field.classList.remove('border-red-200');
    field.classList.add('bg-white');
    
    // Reset input/select styling
    const inputElement = field.querySelector('input, select');
    if (inputElement) {
      inputElement.classList.remove('bg-red-50');
    }
    
    resetField(field);
  }, 1000);
}

/**
 * Reset a field to its display state
 * @param {HTMLElement} field - The field element
 */
function resetField(field) {
  field.classList.remove('editing');
  
  const display = field.querySelector('.field-display');
  const edit = field.querySelector('.field-edit');
  
  if (display && edit) {
    display.classList.remove('hidden');
    edit.classList.add('hidden');
  }
}

/**
 * Update the patient header with the current patient details
 */
function updatePatientHeader() {
  const form = document.getElementById('patient-details-form');
  if (!form) return;

  // Get current values from the form
  const titleSelect = form.querySelector('select[name="patient[title]"]');
  const firstNameInput = form.querySelector('input[name="patient[first_name]"]');
  const lastNameInput = form.querySelector('input[name="patient[last_name]"]');

  // Get current values
  const title = titleSelect ? titleSelect.value : '';
  const firstName = firstNameInput ? firstNameInput.value : '';
  const lastName = lastNameInput ? lastNameInput.value : '';

  // Build the full name with title
  let fullName = '';
  if (title) fullName += title + ' ';
  if (firstName) fullName += firstName + ' ';
  if (lastName) fullName += lastName;

  fullName = fullName.trim();

  // Update the header
  const headerNameElement = document.querySelector('.patient-header-name');
  if (headerNameElement) {
    // Preserve the pronouns part if it exists
    const pronounsSpan = headerNameElement.querySelector('span');
    const pronouns = pronounsSpan ? pronounsSpan.outerHTML : '';

    // Update the header with the new name and preserve pronouns
    headerNameElement.innerHTML = (fullName || 'New Patient') + ' ' + pronouns;
  }
}

/**
 * Update the patient DOB in the header
 * @param {string} dateValue - The date value in YYYY-MM-DD format
 */
function updatePatientDOB(dateValue) {
  const headerDOBElement = document.querySelector('.patient-header-dob');
  if (!headerDOBElement || !dateValue) return;

  // Format the date as DD/MM/YYYY
  const date = new Date(dateValue);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const formattedDate = `${day}/${month}/${year}`;

  // Calculate age
  const ageInYearsAndMonths = calculateAgeInYearsAndMonths(date);

  // Update the header DOB text
  headerDOBElement.innerHTML = `DOB: ${formattedDate} • ${ageInYearsAndMonths}`;
}

/**
 * Calculate age in years and months from a date
 * @param {Date} birthDate - The birth date
 * @returns {string} Age in years and months format
 */
function calculateAgeInYearsAndMonths(birthDate) {
  const today = new Date();

  let years = today.getFullYear() - birthDate.getFullYear();
  let months = today.getMonth() - birthDate.getMonth();

  if (months < 0) {
    years--;
    months += 12;
  }

  // Format the age string
  if (years === 0) {
    return `${months} month${months !== 1 ? 's' : ''}`;
  } else if (months === 0) {
    return `${years} year${years !== 1 ? 's' : ''}`;
  } else {
    return `${years} year${years !== 1 ? 's' : ''} ${months} month${months !== 1 ? 's' : ''}`;
  }
}

// Export functions for use in other files
export { initInlineEditing };
