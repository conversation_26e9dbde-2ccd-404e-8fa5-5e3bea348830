// Phone Fields with International Input
document.addEventListener('DOMContentLoaded', function() {
  if (!document.querySelector('.phone-field-container')) return;

  const form = document.getElementById('patient-details-form');
  const patientId = form?.dataset.patientId;
  if (!patientId) return;

  // Initialize phone field handlers
  initializePhoneFields();

  function initializePhoneFields() {
    document.querySelectorAll('.phone-field-container').forEach(container => {
      const display = container.querySelector('.phone-display');
      const edit = container.querySelector('.phone-edit');
      const input = container.querySelector('.phone-input');
      const saveBtn = container.querySelector('.phone-save');
      const cancelBtn = container.querySelector('.phone-cancel');
      const fieldName = container.dataset.field;

      let itiInstance = null;

      // Click display to edit
      display.addEventListener('click', function() {
        showEditMode();
      });

      // Save button
      saveBtn.addEventListener('click', function() {
        savePhoneField();
      });

      // Cancel button
      cancelBtn.addEventListener('click', function() {
        hideEditMode();
      });

      // Enter key to save
      input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          savePhoneField();
        } else if (e.key === 'Escape') {
          hideEditMode();
        }
      });

      function showEditMode() {
        display.classList.add('hidden');
        edit.classList.remove('hidden');

        // Initialize intl-tel-input if not already done
        if (!itiInstance) {
          itiInstance = window.intlTelInput(input, {
            loadUtils: () => import("https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"),
            initialCountry: "auto",
            geoIpLookup: callback => {
              fetch('https://ipapi.co/json')
                .then(res => res.json())
                .then(data => callback(data.country_code))
                .catch(() => callback("au"));
            },
            preferredCountries: ["au", "nz", "us", "gb"],
            separateDialCode: true,
            autoFormat: true,
            formatOnDisplay: true,
            nationalMode: false, // Keep full international format
            autoPlaceholder: "aggressive"
          });
        }

        // Focus the input
        setTimeout(() => {
          input.focus();
        }, 100);
      }

      function hideEditMode() {
        display.classList.remove('hidden');
        edit.classList.add('hidden');
        
        // Reset input value to original
        const originalValue = display.querySelector('span').textContent.trim();
        if (originalValue !== '-') {
          input.value = originalValue;
        } else {
          input.value = '';
        }
      }

      function savePhoneField() {
        let phoneNumber = '';
        
        if (itiInstance) {
          // Get the full international number
          phoneNumber = itiInstance.getNumber();
          console.log('Full international number:', phoneNumber);
        } else {
          phoneNumber = input.value;
        }

        // Show loading state
        saveBtn.disabled = true;
        saveBtn.textContent = 'Saving...';
        container.classList.add('opacity-50');

        // Prepare data for AJAX request
        const data = {};
        data[`patient[${fieldName}]`] = phoneNumber;

        // Send AJAX request
        $.ajax({
          url: `/admin/patients/${patientId}/update_field`,
          method: 'POST',
          data: data,
          dataType: 'json',
          headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            if (response.success) {
              // Update display with the saved value
              const displaySpan = display.querySelector('span');
              if (phoneNumber) {
                displaySpan.textContent = phoneNumber;
                displaySpan.className = 'text-[14px] font-medium text-gray-800';
              } else {
                displaySpan.textContent = '-';
                displaySpan.className = 'text-[14px] text-gray-400';
              }
              
              hideEditMode();
              toastr.success(`${fieldName.replace(/_/g, ' ')} updated`);
            } else {
              toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
            }
          },
          error: function() {
            toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
          },
          complete: function() {
            // Reset button state
            saveBtn.disabled = false;
            saveBtn.textContent = 'Save';
            container.classList.remove('opacity-50');
          }
        });
      }
    });
  }
});
